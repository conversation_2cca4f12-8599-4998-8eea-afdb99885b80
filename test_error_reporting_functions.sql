-- =====================================================
-- Test Script for Error Reporting Functions
-- =====================================================
-- Run this script after setting up the error reporting schema
-- to verify all functions work correctly

-- =====================================================
-- 1. Test Basic Table Creation
-- =====================================================

-- Verify tables exist
SELECT 'Testing table existence...' as test_step;

SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('crash_reports', 'app_health_metrics')
ORDER BY table_name;

-- =====================================================
-- 2. Test RPC Functions
-- =====================================================

-- Test crash reporting function
SELECT 'Testing report_crash function...' as test_step;

-- Note: Replace 'your-screen-uuid' with an actual screen UUID from your screens table
SELECT report_crash(
    'your-screen-uuid'::UUID,  -- Replace with actual screen UUID
    'test',
    'Test error message from SQL test',
    'Test stack trace line 1\nTest stack trace line 2',
    'test_sql_script',
    'Testing error reporting from SQL',
    false,
    '1.0.0-test',
    'Test Device SQL',
    'Test Platform'
);

-- Test health reporting function
SELECT 'Testing report_health function...' as test_step;

SELECT report_health(
    'your-screen-uuid'::UUID,  -- Replace with actual screen UUID
    true,
    0,
    45.5,
    25.2,
    0,
    0,
    null,
    '1.0.0-test',
    'Test Platform'
);

-- =====================================================
-- 3. Test Data Retrieval Functions
-- =====================================================

-- Test error dashboard function
SELECT 'Testing get_error_dashboard function...' as test_step;

SELECT get_error_dashboard(
    null,  -- Get data for all screens
    24     -- Last 24 hours
);

-- Test recent errors function
SELECT 'Testing get_recent_errors function...' as test_step;

SELECT get_recent_errors(
    null,  -- All screens
    10,    -- Limit to 10 results
    24,    -- Last 24 hours
    null   -- All error types
);

-- =====================================================
-- 4. Test Views
-- =====================================================

-- Test error statistics view
SELECT 'Testing error_statistics view...' as test_step;

SELECT * FROM error_statistics LIMIT 5;

-- Test health summary view
SELECT 'Testing health_summary view...' as test_step;

SELECT * FROM health_summary LIMIT 5;

-- =====================================================
-- 5. Test Cleanup Function
-- =====================================================

-- Test cleanup function (be careful with this in production)
SELECT 'Testing cleanup_error_data function...' as test_step;

-- This will delete old data, use with caution
-- SELECT cleanup_error_data(30, 7);

-- Instead, let's just test the function exists
SELECT 
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'cleanup_error_data';

-- =====================================================
-- 6. Verify Permissions
-- =====================================================

-- Check function permissions
SELECT 'Testing function permissions...' as test_step;

SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'report_crash',
    'report_health', 
    'get_error_dashboard',
    'get_recent_errors',
    'cleanup_error_data'
)
ORDER BY routine_name;

-- =====================================================
-- 7. Sample Data Queries
-- =====================================================

-- Count total records
SELECT 'Checking record counts...' as test_step;

SELECT 
    'crash_reports' as table_name,
    COUNT(*) as record_count
FROM crash_reports
UNION ALL
SELECT 
    'app_health_metrics' as table_name,
    COUNT(*) as record_count
FROM app_health_metrics;

-- Show recent crash reports
SELECT 'Recent crash reports...' as test_step;

SELECT 
    error_type,
    error_message,
    source,
    is_fatal,
    occurred_at
FROM crash_reports 
ORDER BY occurred_at DESC 
LIMIT 5;

-- Show recent health metrics
SELECT 'Recent health metrics...' as test_step;

SELECT 
    is_healthy,
    error_count,
    consecutive_failures,
    recovery_attempts,
    recorded_at
FROM app_health_metrics 
ORDER BY recorded_at DESC 
LIMIT 5;

-- =====================================================
-- 8. Performance Test
-- =====================================================

-- Test function performance
SELECT 'Testing function performance...' as test_step;

-- Time the dashboard function
\timing on
SELECT get_error_dashboard(null, 24);
\timing off

-- =====================================================
-- NOTES FOR TESTING:
-- =====================================================

/*
1. Replace 'your-screen-uuid' with an actual UUID from your screens table
2. The cleanup function test is commented out to prevent accidental data deletion
3. Run this script in sections if you want to examine results step by step
4. Check the results of each test to ensure functions are working correctly
5. If any test fails, check the error message and verify the schema was created correctly

Example of getting a real screen UUID for testing:
SELECT id FROM screens LIMIT 1;

Then replace 'your-screen-uuid' with the actual UUID value.
*/
