-- =====================================================
-- Supabase RPC Functions for Error Reporting
-- =====================================================
-- These functions can be called directly from the Flutter app
-- using Supabase client RPC calls

-- =====================================================
-- 1. RPC FUNCTION: Report Application Crash
-- =====================================================
-- Usage: supabase.rpc('report_crash', { ... })

CREATE OR REPLACE FUNCTION report_crash(
    input_screen_id UUID,
    input_error_type TEXT DEFAULT 'crash',
    input_error_message TEXT DEFAULT '',
    input_stack_trace TEXT DEFAULT NULL,
    input_source TEXT DEFAULT NULL,
    input_context TEXT DEFAULT NULL,
    input_is_fatal BOOLEAN DEFAULT true,
    input_app_version TEXT DEFAULT NULL,
    input_device_info TEXT DEFAULT NULL,
    input_platform TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    report_id UUID;
    result JSON;
BEGIN
    -- Validate input
    IF input_screen_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'screen_id is required'
        );
    END IF;
    
    -- Insert crash report
    INSERT INTO crash_reports (
        screen_id, error_type, error_message, stack_trace, source,
        context, is_fatal, app_version, device_info, platform, occurred_at
    ) VALUES (
        input_screen_id, 
        COALESCE(input_error_type, 'crash'),
        COALESCE(input_error_message, 'Unknown error'),
        input_stack_trace,
        input_source,
        input_context,
        COALESCE(input_is_fatal, true),
        input_app_version,
        input_device_info,
        input_platform,
        NOW()
    ) RETURNING id INTO report_id;
    
    -- Build success response
    result := json_build_object(
        'success', true,
        'report_id', report_id,
        'message', 'Crash report logged successfully'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error response
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- =====================================================
-- 2. RPC FUNCTION: Report Application Health
-- =====================================================
-- Usage: supabase.rpc('report_health', { ... })

CREATE OR REPLACE FUNCTION report_health(
    input_screen_id UUID,
    input_is_healthy BOOLEAN DEFAULT true,
    input_error_count INTEGER DEFAULT 0,
    input_memory_usage DECIMAL DEFAULT NULL,
    input_cpu_usage DECIMAL DEFAULT NULL,
    input_consecutive_failures INTEGER DEFAULT 0,
    input_recovery_attempts INTEGER DEFAULT 0,
    input_last_recovery_type TEXT DEFAULT NULL,
    input_app_version TEXT DEFAULT NULL,
    input_platform TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    metric_id UUID;
    result JSON;
BEGIN
    -- Validate input
    IF input_screen_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'screen_id is required'
        );
    END IF;
    
    -- Insert health metrics
    INSERT INTO app_health_metrics (
        screen_id, is_healthy, error_count, memory_usage_percent,
        cpu_usage_percent, consecutive_failures, recovery_attempts,
        last_recovery_type, app_version, platform, recorded_at
    ) VALUES (
        input_screen_id,
        COALESCE(input_is_healthy, true),
        COALESCE(input_error_count, 0),
        input_memory_usage,
        input_cpu_usage,
        COALESCE(input_consecutive_failures, 0),
        COALESCE(input_recovery_attempts, 0),
        input_last_recovery_type,
        input_app_version,
        input_platform,
        NOW()
    ) RETURNING id INTO metric_id;
    
    -- Build success response
    result := json_build_object(
        'success', true,
        'metric_id', metric_id,
        'message', 'Health metrics logged successfully'
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error response
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- =====================================================
-- 3. RPC FUNCTION: Get Screen Error Dashboard
-- =====================================================
-- Usage: supabase.rpc('get_error_dashboard', { input_screen_id: 'uuid' })

CREATE OR REPLACE FUNCTION get_error_dashboard(
    input_screen_id UUID DEFAULT NULL,
    input_hours_back INTEGER DEFAULT 24
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    -- Build comprehensive dashboard data
    WITH error_stats AS (
        SELECT 
            cr.screen_id,
            s.name as screen_name,
            s.code as screen_code,
            COUNT(*) as total_errors,
            COUNT(CASE WHEN cr.is_fatal = true THEN 1 END) as fatal_errors,
            COUNT(CASE WHEN cr.occurred_at >= NOW() - INTERVAL '1 hour' * input_hours_back THEN 1 END) as recent_errors,
            MAX(cr.occurred_at) as last_error_at,
            MIN(cr.occurred_at) as first_error_at,
            ARRAY_AGG(DISTINCT cr.error_type) as error_types,
            ARRAY_AGG(DISTINCT cr.source) FILTER (WHERE cr.source IS NOT NULL) as error_sources
        FROM crash_reports cr
        JOIN screens s ON cr.screen_id = s.id
        WHERE 1=1 
        GROUP BY cr.screen_id, s.name, s.code
    ),
    health_stats AS (
        SELECT DISTINCT ON (ahm.screen_id)
            ahm.screen_id,
            ahm.is_healthy,
            ahm.error_count as current_error_count,
            ahm.consecutive_failures,
            ahm.recovery_attempts,
            ahm.last_recovery_type,
            ahm.recorded_at as last_health_check
        FROM app_health_metrics ahm
        ORDER BY ahm.screen_id, ahm.recorded_at DESC
    )
    SELECT json_build_object(
        'success', true,
        'data', json_agg(
            json_build_object(
                'screen_id', COALESCE(es.screen_id, hs.screen_id),
                'screen_name', es.screen_name,
                'screen_code', es.screen_code,
                'error_summary', json_build_object(
                    'total_errors', COALESCE(es.total_errors, 0),
                    'fatal_errors', COALESCE(es.fatal_errors, 0),
                    'recent_errors', COALESCE(es.recent_errors, 0),
                    'last_error_at', es.last_error_at,
                    'first_error_at', es.first_error_at,
                    'error_types', COALESCE(es.error_types, ARRAY[]::TEXT[]),
                    'error_sources', COALESCE(es.error_sources, ARRAY[]::TEXT[])
                ),
                'health_summary', json_build_object(
                    'is_healthy', COALESCE(hs.is_healthy, true),
                    'current_error_count', COALESCE(hs.current_error_count, 0),
                    'consecutive_failures', COALESCE(hs.consecutive_failures, 0),
                    'recovery_attempts', COALESCE(hs.recovery_attempts, 0),
                    'last_recovery_type', hs.last_recovery_type,
                    'last_health_check', hs.last_health_check
                )
            )
        ),
        'generated_at', NOW()
    ) INTO result
    FROM error_stats es
    FULL OUTER JOIN health_stats hs ON es.screen_id = hs.screen_id
    WHERE (input_screen_id IS NULL OR COALESCE(es.screen_id, hs.screen_id) = input_screen_id);
    
    RETURN COALESCE(result, json_build_object(
        'success', true,
        'data', json_build_array(),
        'message', 'No data found',
        'generated_at', NOW()
    ));
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- =====================================================
-- 4. RPC FUNCTION: Get Recent Errors
-- =====================================================
-- Usage: supabase.rpc('get_recent_errors', { ... })

CREATE OR REPLACE FUNCTION get_recent_errors(
    input_screen_id UUID DEFAULT NULL,
    input_limit INTEGER DEFAULT 50,
    input_hours_back INTEGER DEFAULT 24,
    input_error_type TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    WITH recent_errors AS (
        SELECT 
            cr.id,
            cr.screen_id,
            s.name as screen_name,
            s.code as screen_code,
            cr.error_type,
            cr.error_message,
            cr.source,
            cr.context,
            cr.is_fatal,
            cr.app_version,
            cr.platform,
            cr.occurred_at,
            cr.created_at
        FROM crash_reports cr
        JOIN screens s ON cr.screen_id = s.id
        WHERE cr.occurred_at >= NOW() - INTERVAL '1 hour' * input_hours_back
        AND (input_screen_id IS NULL OR cr.screen_id = input_screen_id)
        AND (input_error_type IS NULL OR cr.error_type = input_error_type)
        ORDER BY cr.occurred_at DESC
        LIMIT input_limit
    )
    SELECT json_build_object(
        'success', true,
        'data', COALESCE(json_agg(
            json_build_object(
                'id', re.id,
                'screen_id', re.screen_id,
                'screen_name', re.screen_name,
                'screen_code', re.screen_code,
                'error_type', re.error_type,
                'error_message', re.error_message,
                'source', re.source,
                'context', re.context,
                'is_fatal', re.is_fatal,
                'app_version', re.app_version,
                'platform', re.platform,
                'occurred_at', re.occurred_at,
                'created_at', re.created_at
            )
        ), json_build_array()),
        'count', (SELECT COUNT(*) FROM recent_errors),
        'generated_at', NOW()
    ) INTO result
    FROM recent_errors re;
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- =====================================================
-- 5. RPC FUNCTION: Cleanup Old Data
-- =====================================================
-- Usage: supabase.rpc('cleanup_error_data', {})

CREATE OR REPLACE FUNCTION cleanup_error_data(
    input_crash_reports_days INTEGER DEFAULT 30,
    input_health_metrics_days INTEGER DEFAULT 7
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    crash_deleted INTEGER := 0;
    health_deleted INTEGER := 0;
    result JSON;
BEGIN
    -- Delete old crash reports
    DELETE FROM crash_reports
    WHERE created_at < NOW() - INTERVAL '1 day' * input_crash_reports_days;
    GET DIAGNOSTICS crash_deleted = ROW_COUNT;

    -- Delete old health metrics
    DELETE FROM app_health_metrics
    WHERE recorded_at < NOW() - INTERVAL '1 day' * input_health_metrics_days;
    GET DIAGNOSTICS health_deleted = ROW_COUNT;

    result := json_build_object(
        'success', true,
        'crash_reports_deleted', crash_deleted,
        'health_metrics_deleted', health_deleted,
        'total_deleted', crash_deleted + health_deleted,
        'cleaned_at', NOW()
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================
-- Grant execute permissions to authenticated users and service role

GRANT EXECUTE ON FUNCTION report_crash TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION report_health TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_error_dashboard TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION get_recent_errors TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION cleanup_error_data TO service_role; -- Only service role for cleanup

-- =====================================================
-- USAGE EXAMPLES FOR FLUTTER APP:
-- =====================================================

/*
// 1. Report a crash from Flutter
final response = await supabase.rpc('report_crash', {
  'input_screen_id': screenId,
  'input_error_type': 'crash',
  'input_error_message': 'Application crashed during video playback',
  'input_stack_trace': stackTrace.toString(),
  'input_source': 'video_player_widget',
  'input_context': 'Playing video: example.mp4',
  'input_is_fatal': true,
  'input_app_version': '1.0.0',
  'input_device_info': 'Android 12',
  'input_platform': 'Android'
});

// 2. Report health metrics from Flutter
final response = await supabase.rpc('report_health', {
  'input_screen_id': screenId,
  'input_is_healthy': false,
  'input_error_count': 5,
  'input_memory_usage': 85.5,
  'input_cpu_usage': 45.2,
  'input_consecutive_failures': 2,
  'input_recovery_attempts': 1,
  'input_last_recovery_type': 'soft_restart',
  'input_app_version': '1.0.0',
  'input_platform': 'Android'
});

// 3. Get error dashboard data
final response = await supabase.rpc('get_error_dashboard', {
  'input_screen_id': screenId, // Optional: null for all screens
  'input_hours_back': 24
});

// 4. Get recent errors
final response = await supabase.rpc('get_recent_errors', {
  'input_screen_id': screenId, // Optional
  'input_limit': 50,
  'input_hours_back': 24,
  'input_error_type': 'crash' // Optional: null for all types
});
*/
