-- =====================================================
-- Supabase Error Reporting and Crash Monitoring Schema
-- =====================================================
-- This script creates tables and functions for comprehensive
-- error reporting and crash monitoring in the Signage Player

-- =====================================================
-- 1. CRASH REPORTS TABLE
-- =====================================================
-- Stores detailed crash and error reports from the application

CREATE TABLE IF NOT EXISTS crash_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    screen_id UUID NOT NULL,
    error_type VARCHAR(50) NOT NULL, -- 'crash', 'error', 'warning'
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    source VARCHAR(100), -- Source component/service where error occurred
    context TEXT, -- Additional context information
    is_fatal BOOLEAN DEFAULT false,
    app_version VARCHAR(20),
    device_info TEXT,
    platform VARCHAR(20), -- 'Android', 'Windows', 'Linux', etc.
    occurred_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint (assuming screens table exists)
    CONSTRAINT fk_crash_reports_screen_id 
        FOREIGN KEY (screen_id) REFERENCES screens(id) ON DELETE CASCADE
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_crash_reports_screen_id ON crash_reports(screen_id);
CREATE INDEX IF NOT EXISTS idx_crash_reports_error_type ON crash_reports(error_type);
CREATE INDEX IF NOT EXISTS idx_crash_reports_is_fatal ON crash_reports(is_fatal);
CREATE INDEX IF NOT EXISTS idx_crash_reports_occurred_at ON crash_reports(occurred_at);
CREATE INDEX IF NOT EXISTS idx_crash_reports_created_at ON crash_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_crash_reports_platform ON crash_reports(platform);

-- =====================================================
-- 2. APPLICATION HEALTH METRICS TABLE
-- =====================================================
-- Stores periodic health check data from applications

CREATE TABLE IF NOT EXISTS app_health_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    screen_id UUID NOT NULL,
    is_healthy BOOLEAN NOT NULL,
    error_count INTEGER DEFAULT 0,
    memory_usage_percent DECIMAL(5,2), -- e.g., 85.50
    cpu_usage_percent DECIMAL(5,2), -- e.g., 45.25
    consecutive_failures INTEGER DEFAULT 0,
    recovery_attempts INTEGER DEFAULT 0,
    last_recovery_type VARCHAR(20), -- 'soft_restart', 'hard_restart', 'factory_reset'
    app_version VARCHAR(20),
    platform VARCHAR(20),
    recorded_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_app_health_metrics_screen_id 
        FOREIGN KEY (screen_id) REFERENCES screens(id) ON DELETE CASCADE
);

-- Indexes for health metrics
CREATE INDEX IF NOT EXISTS idx_app_health_metrics_screen_id ON app_health_metrics(screen_id);
CREATE INDEX IF NOT EXISTS idx_app_health_metrics_is_healthy ON app_health_metrics(is_healthy);
CREATE INDEX IF NOT EXISTS idx_app_health_metrics_recorded_at ON app_health_metrics(recorded_at);

-- =====================================================
-- 3. ERROR STATISTICS VIEW
-- =====================================================
-- Provides aggregated error statistics per screen

CREATE OR REPLACE VIEW error_statistics AS
SELECT 
    cr.screen_id,
    s.name as screen_name,
    s.code as screen_code,
    COUNT(*) as total_errors,
    COUNT(CASE WHEN cr.is_fatal = true THEN 1 END) as fatal_errors,
    COUNT(CASE WHEN cr.is_fatal = false THEN 1 END) as non_fatal_errors,
    COUNT(CASE WHEN cr.error_type = 'crash' THEN 1 END) as crashes,
    COUNT(CASE WHEN cr.error_type = 'error' THEN 1 END) as errors,
    COUNT(CASE WHEN cr.error_type = 'warning' THEN 1 END) as warnings,
    MAX(cr.occurred_at) as last_error_at,
    MIN(cr.occurred_at) as first_error_at,
    cr.app_version,
    cr.platform
FROM crash_reports cr
JOIN screens s ON cr.screen_id = s.id
GROUP BY cr.screen_id, s.name, s.code, cr.app_version, cr.platform;

-- =====================================================
-- 4. HEALTH SUMMARY VIEW
-- =====================================================
-- Provides latest health status for each screen

CREATE OR REPLACE VIEW health_summary AS
SELECT DISTINCT ON (screen_id)
    ahm.screen_id,
    s.name as screen_name,
    s.code as screen_code,
    ahm.is_healthy,
    ahm.error_count,
    ahm.memory_usage_percent,
    ahm.cpu_usage_percent,
    ahm.consecutive_failures,
    ahm.recovery_attempts,
    ahm.last_recovery_type,
    ahm.app_version,
    ahm.platform,
    ahm.recorded_at
FROM app_health_metrics ahm
JOIN screens s ON ahm.screen_id = s.id
ORDER BY ahm.screen_id, ahm.recorded_at DESC;

-- =====================================================
-- 5. FUNCTIONS FOR ERROR REPORTING
-- =====================================================

-- Function to log crash reports with automatic cleanup
CREATE OR REPLACE FUNCTION log_crash_report(
    p_screen_id UUID,
    p_error_type VARCHAR(50),
    p_error_message TEXT,
    p_stack_trace TEXT DEFAULT NULL,
    p_source VARCHAR(100) DEFAULT NULL,
    p_context TEXT DEFAULT NULL,
    p_is_fatal BOOLEAN DEFAULT false,
    p_app_version VARCHAR(20) DEFAULT NULL,
    p_device_info TEXT DEFAULT NULL,
    p_platform VARCHAR(20) DEFAULT NULL,
    p_occurred_at TIMESTAMPTZ DEFAULT NOW()
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    report_id UUID;
BEGIN
    -- Insert the crash report
    INSERT INTO crash_reports (
        screen_id, error_type, error_message, stack_trace, source, 
        context, is_fatal, app_version, device_info, platform, occurred_at
    ) VALUES (
        p_screen_id, p_error_type, p_error_message, p_stack_trace, p_source,
        p_context, p_is_fatal, p_app_version, p_device_info, p_platform, p_occurred_at
    ) RETURNING id INTO report_id;
    
    -- Clean up old crash reports (keep last 1000 per screen)
    DELETE FROM crash_reports 
    WHERE screen_id = p_screen_id 
    AND id NOT IN (
        SELECT id FROM crash_reports 
        WHERE screen_id = p_screen_id 
        ORDER BY created_at DESC 
        LIMIT 1000
    );
    
    RETURN report_id;
END;
$$;

-- Function to log health metrics
CREATE OR REPLACE FUNCTION log_health_metrics(
    p_screen_id UUID,
    p_is_healthy BOOLEAN,
    p_error_count INTEGER DEFAULT 0,
    p_memory_usage_percent DECIMAL(5,2) DEFAULT NULL,
    p_cpu_usage_percent DECIMAL(5,2) DEFAULT NULL,
    p_consecutive_failures INTEGER DEFAULT 0,
    p_recovery_attempts INTEGER DEFAULT 0,
    p_last_recovery_type VARCHAR(20) DEFAULT NULL,
    p_app_version VARCHAR(20) DEFAULT NULL,
    p_platform VARCHAR(20) DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    metric_id UUID;
BEGIN
    -- Insert health metrics
    INSERT INTO app_health_metrics (
        screen_id, is_healthy, error_count, memory_usage_percent, 
        cpu_usage_percent, consecutive_failures, recovery_attempts,
        last_recovery_type, app_version, platform
    ) VALUES (
        p_screen_id, p_is_healthy, p_error_count, p_memory_usage_percent,
        p_cpu_usage_percent, p_consecutive_failures, p_recovery_attempts,
        p_last_recovery_type, p_app_version, p_platform
    ) RETURNING id INTO metric_id;
    
    -- Clean up old health metrics (keep last 100 per screen)
    DELETE FROM app_health_metrics 
    WHERE screen_id = p_screen_id 
    AND id NOT IN (
        SELECT id FROM app_health_metrics 
        WHERE screen_id = p_screen_id 
        ORDER BY recorded_at DESC 
        LIMIT 100
    );
    
    RETURN metric_id;
END;
$$;

-- Function to get error summary for a screen
CREATE OR REPLACE FUNCTION get_error_summary(
    p_screen_id UUID,
    p_hours_back INTEGER DEFAULT 24
)
RETURNS TABLE (
    total_errors BIGINT,
    fatal_errors BIGINT,
    recent_errors BIGINT,
    last_error_at TIMESTAMPTZ,
    most_common_error TEXT,
    error_rate_per_hour DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_errors,
        COUNT(CASE WHEN cr.is_fatal = true THEN 1 END) as fatal_errors,
        COUNT(CASE WHEN cr.occurred_at >= NOW() - INTERVAL '1 hour' * p_hours_back THEN 1 END) as recent_errors,
        MAX(cr.occurred_at) as last_error_at,
        MODE() WITHIN GROUP (ORDER BY cr.error_message) as most_common_error,
        ROUND(
            COUNT(CASE WHEN cr.occurred_at >= NOW() - INTERVAL '1 hour' * p_hours_back THEN 1 END)::DECIMAL / 
            GREATEST(p_hours_back, 1), 2
        ) as error_rate_per_hour
    FROM crash_reports cr
    WHERE cr.screen_id = p_screen_id;
END;
$$;

-- =====================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================
-- Enable RLS on tables for security

ALTER TABLE crash_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_health_metrics ENABLE ROW LEVEL SECURITY;

-- Policy for crash_reports (allow all operations for service role)
CREATE POLICY "Allow all operations for service role" ON crash_reports
    FOR ALL USING (auth.role() = 'service_role');

-- Policy for app_health_metrics (allow all operations for service role)
CREATE POLICY "Allow all operations for service role" ON app_health_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 7. CLEANUP FUNCTIONS
-- =====================================================

-- Function to cleanup old records
CREATE OR REPLACE FUNCTION cleanup_old_error_data()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER := 0;
    health_deleted_count INTEGER := 0;
BEGIN
    -- Delete crash reports older than 30 days
    DELETE FROM crash_reports
    WHERE created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Delete health metrics older than 7 days
    DELETE FROM app_health_metrics
    WHERE recorded_at < NOW() - INTERVAL '7 days';

    GET DIAGNOSTICS health_deleted_count = ROW_COUNT;

    RETURN deleted_count + health_deleted_count;
END;
$$;

-- =====================================================
-- 8. SAMPLE QUERIES FOR MONITORING
-- =====================================================

-- Query to get screens with most errors in last 24 hours
/*
SELECT 
    s.name,
    s.code,
    COUNT(*) as error_count,
    COUNT(CASE WHEN cr.is_fatal THEN 1 END) as fatal_count
FROM crash_reports cr
JOIN screens s ON cr.screen_id = s.id
WHERE cr.occurred_at >= NOW() - INTERVAL '24 hours'
GROUP BY s.id, s.name, s.code
ORDER BY error_count DESC
LIMIT 10;
*/

-- Query to get unhealthy screens
/*
SELECT 
    screen_name,
    screen_code,
    error_count,
    consecutive_failures,
    recovery_attempts,
    recorded_at
FROM health_summary
WHERE is_healthy = false
ORDER BY consecutive_failures DESC, error_count DESC;
*/

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. Run this script in your Supabase SQL editor
-- 2. Make sure you have a 'screens' table with 'id', 'name', and 'code' columns
-- 3. The service role should have full access to these tables
-- 4. Consider setting up automated cleanup jobs for old data
-- 5. Monitor table sizes and adjust retention policies as needed
