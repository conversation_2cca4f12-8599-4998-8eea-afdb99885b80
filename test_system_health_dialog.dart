// Simple test to verify System Health dialog functionality
// Run this with: flutter run test_system_health_dialog.dart

import 'package:flutter/material.dart';
import 'package:signage/core/services/global_error_handler.dart';
import 'package:signage/core/services/app_health_monitor.dart';
import 'package:signage/core/services/crash_reporter_service.dart';
import 'package:signage/core/services/error_recovery_service.dart';

void main() {
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'System Health Dialog Test',
      home: const TestScreen(),
    );
  }
}

class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('System Health Dialog Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _showSystemHealth(context),
              child: const Text('Test System Health Dialog'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _showForceRecovery(context),
              child: const Text('Test Force Recovery Dialog'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _testErrorHandlerServices(),
              child: const Text('Test Error Handler Services'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSystemHealth(BuildContext context) {
    debugPrint('TestApp: _showSystemHealth called');
    
    try {
      // Get health statistics with fallbacks
      final errorStats = GlobalErrorHandler.getErrorStats();
      final healthStatus = AppHealthMonitor.getHealthStatus();
      final crashStats = CrashReporterService.getCrashStats();
      final recoveryStats = ErrorRecoveryService.getRecoveryStats();
      
      // Provide fallback values if services aren't initialized
      final safeErrorStats = errorStats.isNotEmpty ? errorStats : {
        'errorCount': 0,
        'recentErrorsCount': 0,
        'isInitialized': false,
        'maxErrorsBeforeRestart': 5,
      };
      
      final safeHealthStatus = healthStatus.isNotEmpty ? healthStatus : {
        'isHealthy': true,
        'isMonitoring': false,
        'consecutiveFailures': 0,
        'errorsInLastMinute': 0,
        'lastMemoryUsage': 0.0,
        'lastCpuUsage': 0.0,
        'maxMemoryThreshold': 85.0,
        'maxCpuThreshold': 90.0,
        'maxConsecutiveFailures': 3,
      };
      
      final safeCrashStats = crashStats.isNotEmpty ? crashStats : {
        'isInitialized': false,
        'pendingReports': 0,
        'screenId': 'Not available',
        'appVersion': 'Unknown',
        'deviceInfo': 'Unknown',
      };
      
      final safeRecoveryStats = recoveryStats.isNotEmpty ? recoveryStats : {
        'isInitialized': false,
        'isRecovering': false,
        'recoveryAttempts': 0,
        'maxRecoveryAttempts': 3,
      };

      debugPrint('TestApp: About to show dialog');
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) {
          debugPrint('TestApp: Dialog builder called');
          return AlertDialog(
            title: const Text('System Health'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Health Status: ${safeHealthStatus['isHealthy'] ? 'Healthy' : 'Unhealthy'}'),
                  Text('Error Count: ${safeErrorStats['errorCount']}'),
                  Text('Recent Errors: ${safeErrorStats['recentErrorsCount']}'),
                  Text('Memory Usage: ${(safeHealthStatus['lastMemoryUsage'] as double).toStringAsFixed(1)}%'),
                  Text('CPU Usage: ${(safeHealthStatus['lastCpuUsage'] as double).toStringAsFixed(1)}%'),
                  Text('Recovery Attempts: ${safeRecoveryStats['recoveryAttempts']}'),
                  Text('Pending Crash Reports: ${safeCrashStats['pendingReports']}'),
                  Text('Is Monitoring: ${safeHealthStatus['isMonitoring']}'),
                  Text('Is Recovering: ${safeRecoveryStats['isRecovering']}'),
                  const SizedBox(height: 16),
                  Text('Services Status:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Text('Error Handler: ${safeErrorStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                  Text('Health Monitor: ${safeHealthStatus['isMonitoring'] ? 'Active' : 'Inactive'}'),
                  Text('Crash Reporter: ${safeCrashStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                  Text('Recovery Service: ${safeRecoveryStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: const Text('Close'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  GlobalErrorHandler.resetErrorCount();
                  AppHealthMonitor.resetHealthStatus();
                  ErrorRecoveryService.resetRecoveryAttempts();
                },
                child: const Text('Reset Stats'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      debugPrint('Error showing system health dialog: $e');
      // Show a simple error dialog as fallback
      showDialog(
        context: context,
        builder: (BuildContext dialogContext) => AlertDialog(
          title: const Text('System Health Error'),
          content: Text('Error loading health data: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  void _showForceRecovery(BuildContext context) {
    debugPrint('TestApp: _showForceRecovery called');

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('Force Recovery'),
        content: const Text('This will trigger application recovery. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              debugPrint('TestApp: Soft restart triggered');
            },
            child: const Text('Soft Restart'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              debugPrint('TestApp: Hard restart triggered');
            },
            child: const Text('Hard Restart'),
          ),
        ],
      ),
    );
  }

  void _testErrorHandlerServices() {
    debugPrint('TestApp: Testing error handler services');
    
    // Test each service
    try {
      final errorStats = GlobalErrorHandler.getErrorStats();
      debugPrint('Error Stats: $errorStats');
    } catch (e) {
      debugPrint('Error getting error stats: $e');
    }
    
    try {
      final healthStatus = AppHealthMonitor.getHealthStatus();
      debugPrint('Health Status: $healthStatus');
    } catch (e) {
      debugPrint('Error getting health status: $e');
    }
    
    try {
      final crashStats = CrashReporterService.getCrashStats();
      debugPrint('Crash Stats: $crashStats');
    } catch (e) {
      debugPrint('Error getting crash stats: $e');
    }
    
    try {
      final recoveryStats = ErrorRecoveryService.getRecoveryStats();
      debugPrint('Recovery Stats: $recoveryStats');
    } catch (e) {
      debugPrint('Error getting recovery stats: $e');
    }
  }
}
