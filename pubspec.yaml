name: signage
description: "Digital Signage Player App"
publish_to: 'none'
version: 7.1.0

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^2.3.1
  permission_handler: ^11.0.1
  path_provider: ^2.1.1
  video_player: ^2.7.0
  flutter_isolate: ^2.0.4
  http: ^1.2.1
  package_info_plus: ^8.3.0
  intl: ^0.19.0
  flutter_svg: ^2.0.9  # For SVG image support
  shared_preferences: ^2.5.3
  d4: ^0.4.0
  system_resources: ^1.6.0  # For system health monitoring
  wakelock_plus: ^1.2.8  # For screen wake lock management
  geolocator: ^13.0.1  # For location services and geofencing
  # Media Kit for desktop video playbook
  media_kit: ^1.2.0  # Core media kit package
  media_kit_video: ^1.2.5  # Video rendering for media kit
  media_kit_libs_video: ^1.0.5  # Native libraries for video
  # Serial communication for audience proximity sensor
  flutter_libserialport: ^0.4.0  # Desktop serial communication
  usb_serial: ^0.5.0  # Android USB serial communication

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
