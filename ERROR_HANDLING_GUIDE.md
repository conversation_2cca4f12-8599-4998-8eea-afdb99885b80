# Error Handling and Crash Recovery System Guide

## Overview

This guide explains how to use the comprehensive error handling and crash recovery system implemented in the Signage Player application.

## 🗄️ Database Setup

### 1. Run the SQL Scripts

Execute the following SQL scripts in your Supabase SQL editor:

1. **Main Schema**: `supabase_error_reporting_schema.sql`
   - Creates `crash_reports` and `app_health_metrics` tables
   - Sets up indexes, views, and basic functions
   - Configures Row Level Security (RLS)

2. **RPC Functions**: `supabase_error_reporting_rpc_functions.sql`
   - Creates RPC functions for Flutter app integration
   - Provides error dashboard and reporting functions

### 2. Verify Tables

After running the scripts, verify these tables exist:
- `crash_reports` - Stores error and crash data
- `app_health_metrics` - Stores periodic health check data

### 3. Test the Setup

Use the provided test script `test_error_reporting_functions.sql` to verify everything works:

1. Replace `'your-screen-uuid'` with an actual screen UUID from your screens table
2. Run the test script in sections to verify each component
3. Check that all functions return expected results

### 4. Test RPC Functions

Test the RPC functions in Supabase:

```sql
-- Test crash reporting
SELECT report_crash(
    'your-screen-uuid'::UUID,
    'test',
    'Test error message',
    'Test stack trace',
    'test_source',
    'Test context',
    false,
    '1.0.0',
    'Test Device',
    'Test Platform'
);

-- Test health reporting
SELECT report_health(
    'your-screen-uuid'::UUID,
    true,
    0,
    45.5,
    25.2,
    0,
    0,
    null,
    '1.0.0',
    'Test Platform'
);
```

## 🚀 Application Usage

### Automatic Error Handling

The system automatically handles:

1. **Flutter Framework Errors**: Widget build errors, rendering issues
2. **Async Operation Errors**: Network failures, file I/O errors
3. **Platform Errors**: Native platform exceptions
4. **Application Crashes**: Unhandled exceptions

### Manual Error Reporting

You can manually report errors in your code:

```dart
import 'package:signage/core/services/global_error_handler.dart';

try {
  // Your code here
  await someRiskyOperation();
} catch (error, stackTrace) {
  // Report the error
  GlobalErrorHandler.handleError(
    error,
    stackTrace,
    'your_component_name',
    context: 'Additional context information',
  );
}
```

### Health Monitoring

The system automatically monitors application health and reports metrics to Supabase every 5 minutes.

### Recovery Strategies

The system implements three recovery strategies:

1. **Soft Restart**: Navigate to data loading screen
2. **Hard Restart**: Restart entire application
3. **Factory Reset**: Clear data and restart (last resort)

## 🎛️ System Menu Integration

Access error diagnostics through the system menu (Ctrl+M on desktop, Back button on Android):

### System Health Option
- Shows current error statistics
- Displays memory and CPU usage
- Shows recovery attempt counts
- Provides health status overview

### Force Recovery Option
- Manually trigger soft restart
- Manually trigger hard restart
- Useful for troubleshooting

## 📊 Monitoring Dashboard

### Supabase Dashboard Queries

Use these queries to monitor your signage players:

#### 1. Screens with Most Errors (Last 24 Hours)
```sql
SELECT 
    s.name,
    s.code,
    COUNT(*) as error_count,
    COUNT(CASE WHEN cr.is_fatal THEN 1 END) as fatal_count
FROM crash_reports cr
JOIN screens s ON cr.screen_id = s.id
WHERE cr.occurred_at >= NOW() - INTERVAL '24 hours'
GROUP BY s.id, s.name, s.code
ORDER BY error_count DESC
LIMIT 10;
```

#### 2. Unhealthy Screens
```sql
SELECT 
    screen_name,
    screen_code,
    error_count,
    consecutive_failures,
    recovery_attempts,
    recorded_at
FROM health_summary
WHERE is_healthy = false
ORDER BY consecutive_failures DESC, error_count DESC;
```

#### 3. Error Trends
```sql
SELECT 
    DATE_TRUNC('hour', occurred_at) as hour,
    COUNT(*) as error_count,
    COUNT(CASE WHEN is_fatal THEN 1 END) as fatal_count
FROM crash_reports
WHERE occurred_at >= NOW() - INTERVAL '24 hours'
GROUP BY hour
ORDER BY hour;
```

### Using RPC Functions from External Tools

You can call the RPC functions from external monitoring tools:

```javascript
// Get error dashboard data
const { data, error } = await supabase
  .rpc('get_error_dashboard', {
    input_screen_id: null, // null for all screens
    input_hours_back: 24
  });

// Get recent errors
const { data, error } = await supabase
  .rpc('get_recent_errors', {
    input_limit: 50,
    input_hours_back: 24,
    input_error_type: 'crash'
  });
```

## ⚙️ Configuration

### Error Thresholds

Modify these constants in `GlobalErrorHandler`:
- `_maxErrorsBeforeRestart`: Maximum errors before triggering restart (default: 5)
- `_errorCountResetDuration`: Time to reset error count (default: 5 minutes)

### Health Check Intervals

Modify these constants in `AppHealthMonitor`:
- `_healthCheckInterval`: How often to check health (default: 5 minutes)
- `_memoryCheckInterval`: How often to check memory (default: 1 minute)
- `_maxConsecutiveHealthFailures`: Max failures before recovery (default: 3)

### Data Retention

Modify retention in the SQL functions:
- Crash reports: 30 days (configurable in cleanup function)
- Health metrics: 7 days (configurable in cleanup function)

## 🔧 Troubleshooting

### Common Issues

1. **RPC Functions Not Found**
   - Ensure you've run both SQL scripts
   - Check function permissions in Supabase

2. **Screen ID Not Available**
   - Verify settings.json contains valid screen_id
   - Check Supabase connection

3. **High Memory Usage Warnings**
   - System resources monitoring is simplified
   - Can be enhanced with proper system monitoring libraries

### Debug Mode

Enable debug logging by setting `kDebugMode` to true. This will:
- Print detailed error information to console
- Show error boundaries in UI
- Provide verbose health check logs

### Manual Recovery

If the automatic recovery fails:
1. Use system menu "Force Recovery" option
2. Manually restart the application
3. Check Supabase logs for error patterns
4. Clear application data if necessary

## 📈 Best Practices

1. **Regular Monitoring**: Check Supabase dashboard daily
2. **Error Pattern Analysis**: Look for recurring error sources
3. **Health Trends**: Monitor health metrics over time
4. **Proactive Recovery**: Address issues before they cause crashes
5. **Data Cleanup**: Run cleanup functions regularly to manage storage

## 🔄 Maintenance

### Weekly Tasks
- Review error dashboard
- Check for unhealthy screens
- Analyze error trends

### Monthly Tasks
- Run data cleanup functions
- Review and adjust error thresholds
- Update recovery strategies if needed

### Quarterly Tasks
- Analyze long-term error patterns
- Optimize health check intervals
- Review and update documentation

## 📞 Support

For issues with the error handling system:
1. Check the error logs in Supabase
2. Review the system health dashboard
3. Use the manual recovery options
4. Contact support with specific error IDs and timestamps

---

This error handling system provides comprehensive monitoring and automatic recovery capabilities for your signage player deployment. Regular monitoring and maintenance will ensure optimal performance and reliability.
