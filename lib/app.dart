import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signage/core/controllers/system_menu_controller.dart';
import 'package:signage/ui/screens/splash_screen.dart';
import 'package:signage/ui/widgets/system_menu_widget.dart';
import 'package:signage/core/services/global_error_handler.dart';
import 'package:signage/core/services/app_health_monitor.dart';

class SignageApp extends StatefulWidget {
  const SignageApp({super.key});

  @override
  State<SignageApp> createState() => _SignageAppState();
}

class _SignageAppState extends State<SignageApp> {
  // System menu controller
  final SystemMenuController _systemMenuController = SystemMenuController();

  // State for system menu
  bool _showSystemMenu = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  @override
  void dispose() {
    // Dispose services
    AppHealthMonitor.dispose();

    // Dispose system menu controller
    _systemMenuController.dispose();
    super.dispose();
  }

  /// Initialize error handling and monitoring services
  Future<void> _initializeServices() async {
    try {
      // Start health monitoring
      await AppHealthMonitor.initialize();
      AppHealthMonitor.startMonitoring();
    } catch (e) {
      // If service initialization fails, log it but continue
      GlobalErrorHandler.handleError(e, StackTrace.current, 'service_initialization');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Set system UI to be fullscreen without title bar
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    return MaterialApp(
      title: 'Signage Player',
      debugShowCheckedModeBanner: false,
      navigatorKey: SystemMenuController.navigatorKey,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      builder: (context, child) {
        // Initialize system menu controller
        _systemMenuController.initialize(
          context,
          onShowMenu: _toggleSystemMenu,
        );

        return Material(
          type: MaterialType.transparency,
          child: GlobalErrorHandler.createErrorBoundary(
            context: 'main_app_content',
            child: Stack(
              children: [
                // Main app content wrapped in error boundary
                GlobalErrorHandler.createErrorBoundary(
                  context: 'app_child',
                  child: child!,
                  fallbackWidget: Container(
                    color: Colors.black,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 64,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Application Error',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'The application encountered an error.\nRestarting automatically...',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // System menu widget
                SystemMenuWidget(
                  isMenuVisible: _showSystemMenu,
                  onMenuVisibilityChanged: (isVisible) {
                    setState(() {
                      _showSystemMenu = isVisible;
                    });
                  },
                  onRestartPlayer: () => _systemMenuController.restartPlayer(context),
                ),
              ],
            ),
          ),
        );
      },
      home: const SplashScreen(),
    );
  }

  /// Toggle system menu visibility
  void _toggleSystemMenu() {
    setState(() {
      _showSystemMenu = !_showSystemMenu;
    });
  }
}
