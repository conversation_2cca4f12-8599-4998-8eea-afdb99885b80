import 'package:flutter/material.dart';
import 'package:signage/app.dart';
import 'package:signage/utils/multi_monitor_handler.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:media_kit/media_kit.dart';
import 'package:signage/core/services/global_error_handler.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize global error handler first
  await GlobalErrorHandler.initialize();

  try {
    // Initialize media kit for desktop platforms
    if (PlatformUtils.isDesktop) {
      MediaKit.ensureInitialized();
    }

    // Initialize multi-monitor handler
    await MultiMonitorHandler.initialize();

    runApp(const SignageApp());
  } catch (error, stackTrace) {
    // Handle any initialization errors
    GlobalErrorHandler.handleError(error, stackTrace, 'main_initialization');

    // Still try to run the app with basic functionality
    runApp(const SignageApp());
  }
}
