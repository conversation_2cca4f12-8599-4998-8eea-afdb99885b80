import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:signage/core/services/supabase_service.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/utils/platform_utils.dart';

/// Service for reporting crashes and errors to Supabase
class CrashReporterService {
  static bool _isInitialized = false;
  static String? _screenId;
  static String? _appVersion;
  static String? _deviceInfo;
  static final List<Map<String, dynamic>> _pendingReports = [];
  static Timer? _reportingTimer;

  /// Initialize the crash reporter service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load screen ID from settings
      final settings = await Settings.load();
      _screenId = settings?.screenId;

      // Get app version
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = packageInfo.version;

      // Get device info
      _deviceInfo = await _getDeviceInfo();

      // Start periodic reporting timer
      _startReportingTimer();

      _isInitialized = true;
      debugPrint('CrashReporterService: Initialized successfully');
    } catch (e) {
      debugPrint('CrashReporterService: Failed to initialize: $e');
    }
  }

  /// Report a fatal crash
  static void reportCrash({
    required dynamic error,
    StackTrace? stackTrace,
    required String source,
    String? context,
    bool isFatal = true,
  }) {
    _addReport(
      type: 'crash',
      error: error,
      stackTrace: stackTrace,
      source: source,
      context: context,
      isFatal: isFatal,
    );
  }

  /// Report a non-fatal error
  static void reportError({
    required dynamic error,
    StackTrace? stackTrace,
    required String source,
    String? context,
  }) {
    _addReport(
      type: 'error',
      error: error,
      stackTrace: stackTrace,
      source: source,
      context: context,
      isFatal: false,
    );
  }

  /// Add a report to the pending queue
  static void _addReport({
    required String type,
    required dynamic error,
    StackTrace? stackTrace,
    required String source,
    String? context,
    bool isFatal = false,
  }) {
    try {
      final report = {
        'type': type,
        'error': error.toString(),
        'stackTrace': stackTrace?.toString(),
        'source': source,
        'context': context,
        'isFatal': isFatal,
        'timestamp': DateTime.now().toUtc().toIso8601String(),
        'screenId': _screenId,
        'appVersion': _appVersion,
        'deviceInfo': _deviceInfo,
        'platform': PlatformUtils.platformName,
      };

      _pendingReports.add(report);

      // Limit pending reports to prevent memory issues
      if (_pendingReports.length > 100) {
        _pendingReports.removeAt(0);
      }

      // Try to send immediately for fatal crashes
      if (isFatal) {
        _sendPendingReports();
      }
    } catch (e) {
      debugPrint('CrashReporterService: Failed to add report: $e');
    }
  }

  /// Start the periodic reporting timer
  static void _startReportingTimer() {
    _reportingTimer?.cancel();
    _reportingTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _sendPendingReports();
    });
  }

  /// Send pending reports to Supabase
  static Future<void> _sendPendingReports() async {
    if (_pendingReports.isEmpty || _screenId == null) return;

    try {
      final reportsToSend = List<Map<String, dynamic>>.from(_pendingReports);
      _pendingReports.clear();

      for (final report in reportsToSend) {
        await _sendSingleReport(report);
      }
    } catch (e) {
      debugPrint('CrashReporterService: Failed to send reports: $e');
      // Add reports back to queue for retry
      _pendingReports.addAll(_pendingReports);
    }
  }

  /// Send a single report to Supabase using RPC function
  static Future<void> _sendSingleReport(Map<String, dynamic> report) async {
    try {
      // Use the RPC function to report crash
      final response = await SupabaseService.client.rpc('report_crash', params: {
        'input_screen_id': report['screenId'],
        'input_error_type': report['type'],
        'input_error_message': report['error'],
        'input_stack_trace': report['stackTrace'],
        'input_source': report['source'],
        'input_context': report['context'],
        'input_is_fatal': report['isFatal'],
        'input_app_version': report['appVersion'],
        'input_device_info': report['deviceInfo'],
        'input_platform': report['platform'],
      });

      // Check if the RPC call was successful
      if (response != null && response['success'] == true) {
        debugPrint('CrashReporterService: Report sent successfully - ID: ${response['report_id']}');
      } else {
        throw Exception('RPC call failed: ${response?['error'] ?? 'Unknown error'}');
      }

      // Also log through logging service
      final loggingService = LoggingService();
      loggingService.logError(
        '${report['type'].toString().toUpperCase()}: ${report['error']} (Source: ${report['source']})'
      );
    } catch (e) {
      debugPrint('CrashReporterService: Failed to send single report: $e');
      // Re-add to pending reports for retry
      _pendingReports.add(report);
    }
  }

  /// Get device information
  static Future<String> _getDeviceInfo() async {
    try {
      final info = <String>[];
      
      if (PlatformUtils.isAndroid) {
        info.add('Android');
      } else if (PlatformUtils.isWindows) {
        info.add('Windows');
      } else if (PlatformUtils.isLinux) {
        info.add('Linux');
      } else {
        info.add('Unknown');
      }

      // Add additional platform-specific info if available
      if (Platform.isAndroid || Platform.isIOS) {
        info.add('Mobile');
      } else {
        info.add('Desktop');
      }

      return info.join(' ');
    } catch (e) {
      return 'Unknown Device';
    }
  }

  /// Get crash statistics
  static Map<String, dynamic> getCrashStats() {
    return {
      'isInitialized': _isInitialized,
      'pendingReports': _pendingReports.length,
      'screenId': _screenId,
      'appVersion': _appVersion,
      'deviceInfo': _deviceInfo,
    };
  }

  /// Force send all pending reports
  static Future<void> flushReports() async {
    await _sendPendingReports();
  }

  /// Report health metrics to Supabase
  static Future<void> reportHealthMetrics({
    required bool isHealthy,
    int errorCount = 0,
    double? memoryUsage,
    double? cpuUsage,
    int consecutiveFailures = 0,
    int recoveryAttempts = 0,
    String? lastRecoveryType,
  }) async {
    if (_screenId == null) {
      debugPrint('CrashReporterService: Cannot report health - screen ID not available');
      return;
    }

    try {
      final response = await SupabaseService.client.rpc('report_health', params: {
        'input_screen_id': _screenId,
        'input_is_healthy': isHealthy,
        'input_error_count': errorCount,
        'input_memory_usage': memoryUsage,
        'input_cpu_usage': cpuUsage,
        'input_consecutive_failures': consecutiveFailures,
        'input_recovery_attempts': recoveryAttempts,
        'input_last_recovery_type': lastRecoveryType,
        'input_app_version': _appVersion,
        'input_platform': PlatformUtils.platformName,
      });

      if (response != null && response['success'] == true) {
        debugPrint('CrashReporterService: Health metrics sent successfully - ID: ${response['metric_id']}');
      } else {
        debugPrint('CrashReporterService: Failed to send health metrics: ${response?['error']}');
      }
    } catch (e) {
      debugPrint('CrashReporterService: Error sending health metrics: $e');
    }
  }

  /// Clear all pending reports
  static void clearPendingReports() {
    _pendingReports.clear();
  }

  /// Dispose resources
  static void dispose() {
    _reportingTimer?.cancel();
    _pendingReports.clear();
    _isInitialized = false;
  }
}
