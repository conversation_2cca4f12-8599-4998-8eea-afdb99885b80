import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/services/crash_reporter_service.dart';
import 'package:signage/core/services/error_recovery_service.dart';

/// Global error handler for catching and managing all application errors
class GlobalErrorHandler {
  static bool _isInitialized = false;
  static final List<String> _recentErrors = [];
  static const int _maxRecentErrors = 10;
  static Timer? _errorThrottleTimer;
  static int _errorCount = 0;
  static const int _maxErrorsBeforeRestart = 5;
  static const Duration _errorCountResetDuration = Duration(minutes: 5);

  /// Initialize the global error handler
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Set up Flutter error handling
      FlutterError.onError = (FlutterErrorDetails details) {
        handleFlutterError(details);
      };

      // Set up platform dispatcher error handling for async errors
      PlatformDispatcher.instance.onError = (error, stack) {
        handleError(error, stack, 'platform_dispatcher');
        return true;
      };

      // Set up isolate error handling
      Isolate.current.addErrorListener(RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        final error = errorAndStacktrace.first;
        final stackTrace = StackTrace.fromString(errorAndStacktrace.last.toString());
        handleError(error, stackTrace, 'isolate');
      }).sendPort);

      // Initialize crash reporter
      await CrashReporterService.initialize();

      // Initialize error recovery service
      await ErrorRecoveryService.initialize();

      _isInitialized = true;
      debugPrint('GlobalErrorHandler: Initialized successfully');
    } catch (e, stackTrace) {
      debugPrint('GlobalErrorHandler: Failed to initialize: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// Handle Flutter framework errors
  static void handleFlutterError(FlutterErrorDetails details) {
    // Log to console in debug mode
    if (kDebugMode) {
      FlutterError.presentError(details);
    }

    // Handle the error through our system
    handleError(
      details.exception,
      details.stack,
      'flutter_framework',
      context: details.context?.toString(),
      library: details.library,
    );
  }

  /// Handle general errors with context
  static void handleError(
    dynamic error,
    StackTrace? stackTrace,
    String source, {
    String? context,
    String? library,
    bool isFatal = false,
  }) {
    try {
      final errorMessage = error.toString();

      // Prevent error loops by checking for recent duplicates
      if (_isRecentError(errorMessage)) {
        return;
      }

      // Add to recent errors
      _addRecentError(errorMessage);

      // Increment error count
      _incrementErrorCount();

      // Log the error
      _logError(error, stackTrace, source, context: context, library: library);

      // Report crash if it's a fatal error or we've had too many errors
      if (isFatal || _errorCount >= _maxErrorsBeforeRestart) {
        CrashReporterService.reportCrash(
          error: error,
          stackTrace: stackTrace,
          source: source,
          context: context,
          isFatal: isFatal || _errorCount >= _maxErrorsBeforeRestart,
        );

        // Trigger recovery if we've had too many errors
        if (_errorCount >= _maxErrorsBeforeRestart) {
          _triggerRecovery('Too many errors detected');
        }
      } else {
        // Report as non-fatal error
        CrashReporterService.reportError(
          error: error,
          stackTrace: stackTrace,
          source: source,
          context: context,
        );
      }
    } catch (handlingError) {
      // If error handling itself fails, at least log to console
      debugPrint('GlobalErrorHandler: Error in error handling: $handlingError');
      debugPrint('Original error: $error');
    }
  }

  /// Log error to logging service
  static void _logError(
    dynamic error,
    StackTrace? stackTrace,
    String source, {
    String? context,
    String? library,
  }) {
    try {
      final loggingService = LoggingService();
      final errorDetails = [
        'Source: $source',
        if (context != null) 'Context: $context',
        if (library != null) 'Library: $library',
        'Error: $error',
        if (stackTrace != null) 'Stack: ${stackTrace.toString().split('\n').take(5).join('\n')}',
      ].join(' | ');

      loggingService.logError(errorDetails);
    } catch (e) {
      debugPrint('GlobalErrorHandler: Failed to log error: $e');
    }
  }

  /// Check if this error was recently handled
  static bool _isRecentError(String errorMessage) {
    return _recentErrors.contains(errorMessage);
  }

  /// Add error to recent errors list
  static void _addRecentError(String errorMessage) {
    _recentErrors.add(errorMessage);
    if (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
  }

  /// Increment error count and set reset timer
  static void _incrementErrorCount() {
    _errorCount++;
    
    // Reset error count after specified duration
    _errorThrottleTimer?.cancel();
    _errorThrottleTimer = Timer(_errorCountResetDuration, () {
      _errorCount = 0;
      _recentErrors.clear();
    });
  }

  /// Trigger recovery mechanism
  static void _triggerRecovery(String reason) {
    debugPrint('GlobalErrorHandler: Triggering recovery - $reason');
    ErrorRecoveryService.triggerRecovery(reason);
  }

  /// Handle specific widget errors gracefully
  static Widget handleWidgetError(
    BuildContext context,
    FlutterErrorDetails details, {
    Widget? fallbackWidget,
  }) {
    // Log the widget error
    handleFlutterError(details);

    // Return fallback widget or default error widget
    return fallbackWidget ?? 
      Container(
        color: Colors.black,
        child: const Center(
          child: Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
        ),
      );
  }

  /// Create error boundary widget
  static Widget createErrorBoundary({
    required Widget child,
    Widget? fallbackWidget,
    String? context,
  }) {
    return Builder(
      builder: (BuildContext builderContext) {
        try {
          return child;
        } catch (error, stackTrace) {
          handleError(
            error,
            stackTrace,
            'widget_error_boundary',
            context: context ?? builderContext.toString(),
          );

          return fallbackWidget ?? 
            Container(
              color: Colors.black,
              child: const Center(
                child: Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
              ),
            );
        }
      },
    );
  }

  /// Get current error statistics
  static Map<String, dynamic> getErrorStats() {
    return {
      'errorCount': _errorCount,
      'recentErrorsCount': _recentErrors.length,
      'isInitialized': _isInitialized,
      'maxErrorsBeforeRestart': _maxErrorsBeforeRestart,
    };
  }

  /// Reset error count manually
  static void resetErrorCount() {
    _errorCount = 0;
    _recentErrors.clear();
    _errorThrottleTimer?.cancel();
  }

  /// Dispose resources
  static void dispose() {
    _errorThrottleTimer?.cancel();
    _recentErrors.clear();
    _errorCount = 0;
    _isInitialized = false;
  }
}
