import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/core/services/error_recovery_service.dart';
import 'package:signage/core/services/global_error_handler.dart';
import 'package:signage/core/services/crash_reporter_service.dart';


/// Service for monitoring application health and triggering recovery when needed
class AppHealthMonitor {
  static bool _isInitialized = false;
  static bool _isMonitoring = false;
  static Timer? _healthCheckTimer;
  static Timer? _memoryCheckTimer;
  static const Duration _healthCheckInterval = Duration(minutes: 5);
  static const Duration _memoryCheckInterval = Duration(minutes: 1);

  // Health thresholds
  static const double _maxMemoryUsagePercent = 85.0;
  static const double _maxCpuUsagePercent = 90.0;
  static const int _maxConsecutiveHealthFailures = 3;
  static const int _maxErrorsPerMinute = 10;

  // Health tracking
  static int _consecutiveHealthFailures = 0;
  static int _errorsInLastMinute = 0;
  static DateTime _lastErrorCountReset = DateTime.now();
  static double _lastMemoryUsage = 0.0;
  static double _lastCpuUsage = 0.0;
  static bool _isHealthy = true;

  /// Initialize the health monitor
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isInitialized = true;
      debugPrint('AppHealthMonitor: Initialized successfully');
    } catch (e) {
      debugPrint('AppHealthMonitor: Failed to initialize: $e');
    }
  }

  /// Start health monitoring
  static void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    debugPrint('AppHealthMonitor: Starting health monitoring');

    // Start periodic health checks
    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (timer) {
      _performHealthCheck();
    });

    // Start memory monitoring
    _memoryCheckTimer = Timer.periodic(_memoryCheckInterval, (timer) {
      _checkMemoryUsage();
    });
  }

  /// Stop health monitoring
  static void stopMonitoring() {
    _isMonitoring = false;
    _healthCheckTimer?.cancel();
    _memoryCheckTimer?.cancel();
    debugPrint('AppHealthMonitor: Stopped health monitoring');
  }

  /// Perform comprehensive health check
  static Future<void> _performHealthCheck() async {
    try {
      final healthIssues = <String>[];

      // Check system resources
      await _checkSystemResources(healthIssues);

      // Check error rates
      _checkErrorRates(healthIssues);

      // Check application state
      _checkApplicationState(healthIssues);

      // Evaluate overall health
      if (healthIssues.isEmpty) {
        _onHealthCheckPassed();
      } else {
        _onHealthCheckFailed(healthIssues);
      }
    } catch (e) {
      debugPrint('AppHealthMonitor: Health check failed: $e');
      _onHealthCheckFailed(['Health check exception: $e']);
    }
  }

  /// Check system resources (CPU, memory, disk)
  static Future<void> _checkSystemResources(List<String> healthIssues) async {
    try {
      // For now, we'll skip detailed system resource monitoring
      // This can be enhanced later with proper system monitoring
      debugPrint('AppHealthMonitor: System resource check completed');
    } catch (e) {
      healthIssues.add('Failed to check system resources: $e');
    }
  }

  /// Check error rates
  static void _checkErrorRates(List<String> healthIssues) {
    try {
      // Reset error count every minute
      final now = DateTime.now();
      if (now.difference(_lastErrorCountReset).inMinutes >= 1) {
        _errorsInLastMinute = 0;
        _lastErrorCountReset = now;
      }

      // Get current error stats from global error handler
      final errorStats = GlobalErrorHandler.getErrorStats();
      final currentErrorCount = errorStats['errorCount'] as int;

      if (currentErrorCount > _maxErrorsPerMinute) {
        healthIssues.add('High error rate: $currentErrorCount errors per minute');
      }
    } catch (e) {
      healthIssues.add('Failed to check error rates: $e');
    }
  }

  /// Check application state
  static void _checkApplicationState(List<String> healthIssues) {
    try {
      // Check if error recovery is running
      if (ErrorRecoveryService.isRecovering) {
        healthIssues.add('Error recovery in progress');
      }

      // Add more application-specific health checks here
      // For example:
      // - Check if critical services are running
      // - Check if network connectivity is available
      // - Check if required files exist
    } catch (e) {
      healthIssues.add('Failed to check application state: $e');
    }
  }

  /// Check memory usage specifically
  static Future<void> _checkMemoryUsage() async {
    try {
      // For now, we'll skip detailed memory monitoring
      // This can be enhanced later with proper system monitoring
      debugPrint('AppHealthMonitor: Memory usage check completed');
    } catch (e) {
      debugPrint('AppHealthMonitor: Failed to check memory usage: $e');
    }
  }

  /// Called when health check passes
  static void _onHealthCheckPassed() {
    if (!_isHealthy) {
      debugPrint('AppHealthMonitor: Health restored');
      _logHealthEvent('Health check passed - system healthy');
    }

    _isHealthy = true;
    _consecutiveHealthFailures = 0;

    // Report health metrics to Supabase
    _reportHealthMetrics();
  }

  /// Called when health check fails
  static void _onHealthCheckFailed(List<String> healthIssues) {
    _isHealthy = false;
    _consecutiveHealthFailures++;

    final issuesText = healthIssues.join(', ');
    debugPrint('AppHealthMonitor: Health check failed ($_consecutiveHealthFailures/$_maxConsecutiveHealthFailures): $issuesText');

    _logHealthEvent('Health check failed: $issuesText');

    // Report health metrics to Supabase
    _reportHealthMetrics();

    // Trigger recovery if we've had too many consecutive failures
    if (_consecutiveHealthFailures >= _maxConsecutiveHealthFailures) {
      debugPrint('AppHealthMonitor: Too many consecutive health failures, triggering recovery');
      ErrorRecoveryService.triggerRecovery(
        'Health check failures: $issuesText',
        strategy: RecoveryStrategy.softRestart,
      );
      _consecutiveHealthFailures = 0; // Reset after triggering recovery
    }
  }

  /// Log health events
  static void _logHealthEvent(String message) {
    try {
      final loggingService = LoggingService();
      loggingService.logError('HEALTH: $message');
    } catch (e) {
      debugPrint('AppHealthMonitor: Failed to log health event: $e');
    }
  }

  /// Report health metrics to Supabase
  static void _reportHealthMetrics() {
    try {
      CrashReporterService.reportHealthMetrics(
        isHealthy: _isHealthy,
        errorCount: _errorsInLastMinute,
        memoryUsage: _lastMemoryUsage > 0 ? _lastMemoryUsage : null,
        cpuUsage: _lastCpuUsage > 0 ? _lastCpuUsage : null,
        consecutiveFailures: _consecutiveHealthFailures,
        recoveryAttempts: 0, // This would need to be tracked separately
        lastRecoveryType: null, // This would need to be tracked separately
      );
    } catch (e) {
      debugPrint('AppHealthMonitor: Failed to report health metrics: $e');
    }
  }

  /// Report error to health monitor
  static void reportError() {
    _errorsInLastMinute++;
  }

  /// Get current health status
  static Map<String, dynamic> getHealthStatus() {
    return {
      'isHealthy': _isHealthy,
      'isMonitoring': _isMonitoring,
      'consecutiveFailures': _consecutiveHealthFailures,
      'errorsInLastMinute': _errorsInLastMinute,
      'lastMemoryUsage': _lastMemoryUsage,
      'lastCpuUsage': _lastCpuUsage,
      'maxMemoryThreshold': _maxMemoryUsagePercent,
      'maxCpuThreshold': _maxCpuUsagePercent,
      'maxConsecutiveFailures': _maxConsecutiveHealthFailures,
    };
  }

  /// Force health check
  static Future<void> forceHealthCheck() async {
    await _performHealthCheck();
  }

  /// Reset health status
  static void resetHealthStatus() {
    _isHealthy = true;
    _consecutiveHealthFailures = 0;
    _errorsInLastMinute = 0;
    _lastErrorCountReset = DateTime.now();
  }

  /// Dispose resources
  static void dispose() {
    stopMonitoring();
    _isInitialized = false;
  }
}
