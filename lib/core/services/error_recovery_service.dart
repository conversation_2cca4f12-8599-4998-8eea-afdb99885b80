import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:signage/core/controllers/system_menu_controller.dart';
import 'package:signage/core/services/logging_service.dart';
import 'package:signage/ui/screens/data_loading_screen.dart';
import 'package:signage/ui/screens/splash_screen.dart';
import 'package:signage/utils/platform_utils.dart';

/// Recovery strategies
enum RecoveryStrategy {
  softRestart,    // Navigate to data loading screen
  hardRestart,    // Restart entire application
  factoryReset,   // Clear all data and restart
}

/// Service for handling error recovery and application restart
class ErrorRecoveryService {
  static bool _isInitialized = false;
  static bool _isRecovering = false;
  static int _recoveryAttempts = 0;
  static const int _maxRecoveryAttempts = 3;
  static Timer? _recoveryTimer;
  static const Duration _recoveryDelay = Duration(seconds: 5);

  /// Initialize the error recovery service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _isInitialized = true;
      debugPrint('ErrorRecoveryService: Initialized successfully');
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to initialize: $e');
    }
  }

  /// Trigger recovery based on the error severity
  static void triggerRecovery(String reason, {RecoveryStrategy? strategy}) {
    if (_isRecovering) {
      debugPrint('ErrorRecoveryService: Recovery already in progress');
      return;
    }

    _isRecovering = true;
    _recoveryAttempts++;

    debugPrint('ErrorRecoveryService: Triggering recovery (attempt $_recoveryAttempts) - $reason');

    // Log recovery attempt
    try {
      final loggingService = LoggingService();
      loggingService.logError('Recovery triggered: $reason (attempt $_recoveryAttempts)');
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to log recovery attempt: $e');
    }

    // Determine recovery strategy
    final recoveryStrategy = strategy ?? _determineRecoveryStrategy();

    // Execute recovery with delay to allow current operations to complete
    _recoveryTimer?.cancel();
    _recoveryTimer = Timer(_recoveryDelay, () {
      _executeRecovery(recoveryStrategy, reason);
    });
  }

  /// Determine the appropriate recovery strategy
  static RecoveryStrategy _determineRecoveryStrategy() {
    if (_recoveryAttempts <= 1) {
      return RecoveryStrategy.softRestart;
    } else if (_recoveryAttempts <= 2) {
      return RecoveryStrategy.hardRestart;
    } else {
      return RecoveryStrategy.factoryReset;
    }
  }

  /// Execute the recovery strategy
  static void _executeRecovery(RecoveryStrategy strategy, String reason) {
    try {
      switch (strategy) {
        case RecoveryStrategy.softRestart:
          _performSoftRestart(reason);
          break;
        case RecoveryStrategy.hardRestart:
          _performHardRestart(reason);
          break;
        case RecoveryStrategy.factoryReset:
          _performFactoryReset(reason);
          break;
      }
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to execute recovery: $e');
      // If recovery fails, try hard restart as last resort
      _performHardRestart('Recovery execution failed: $e');
    }
  }

  /// Perform soft restart (navigate to data loading screen)
  static void _performSoftRestart(String reason) {
    try {
      debugPrint('ErrorRecoveryService: Performing soft restart - $reason');

      // Try to navigate using the system menu controller
      final navigatorKey = SystemMenuController.navigatorKey;
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const DataLoadingScreen()),
          (route) => false,
        );
        _onRecoveryComplete('Soft restart completed');
      } else {
        // Fallback to hard restart if navigation fails
        _performHardRestart('Soft restart failed - navigator not available');
      }
    } catch (e) {
      debugPrint('ErrorRecoveryService: Soft restart failed: $e');
      _performHardRestart('Soft restart failed: $e');
    }
  }

  /// Perform hard restart (restart entire application)
  static void _performHardRestart(String reason) {
    try {
      debugPrint('ErrorRecoveryService: Performing hard restart - $reason');

      if (PlatformUtils.isAndroid) {
        // On Android, restart the activity
        _restartAndroidApp();
      } else if (PlatformUtils.isDesktop) {
        // On desktop, exit and let the system restart
        _restartDesktopApp();
      } else {
        // Fallback: navigate to splash screen
        _navigateToSplash();
      }
    } catch (e) {
      debugPrint('ErrorRecoveryService: Hard restart failed: $e');
      _performFactoryReset('Hard restart failed: $e');
    }
  }

  /// Perform factory reset (clear data and restart)
  static void _performFactoryReset(String reason) {
    try {
      debugPrint('ErrorRecoveryService: Performing factory reset - $reason');

      // Clear application data
      _clearApplicationData().then((_) {
        // Navigate to splash screen for re-registration
        _navigateToSplash();
        _onRecoveryComplete('Factory reset completed');
      }).catchError((e) {
        debugPrint('ErrorRecoveryService: Factory reset failed: $e');
        // Last resort: just navigate to splash
        _navigateToSplash();
      });
    } catch (e) {
      debugPrint('ErrorRecoveryService: Factory reset failed: $e');
      _navigateToSplash();
    }
  }

  /// Restart Android app
  static void _restartAndroidApp() {
    // This would typically require platform-specific implementation
    // For now, navigate to splash screen
    _navigateToSplash();
  }

  /// Restart desktop app
  static void _restartDesktopApp() {
    // Exit the application - the system should restart it
    exit(1);
  }

  /// Navigate to splash screen
  static void _navigateToSplash() {
    try {
      final navigatorKey = SystemMenuController.navigatorKey;
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const SplashScreen()),
          (route) => false,
        );
        _onRecoveryComplete('Navigated to splash screen');
      }
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to navigate to splash: $e');
    }
  }

  /// Clear application data
  static Future<void> _clearApplicationData() async {
    try {
      // Clear settings, data files, and cached content
      // This is a placeholder - implement based on your storage structure
      debugPrint('ErrorRecoveryService: Clearing application data');
      
      // You might want to clear:
      // - Settings files
      // - Downloaded media
      // - Cached data
      // - Temporary files
      
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to clear application data: $e');
    }
  }

  /// Called when recovery is complete
  static void _onRecoveryComplete(String message) {
    debugPrint('ErrorRecoveryService: $message');
    _isRecovering = false;
    
    // Reset recovery attempts after successful recovery
    Timer(const Duration(minutes: 10), () {
      _recoveryAttempts = 0;
    });

    try {
      final loggingService = LoggingService();
      loggingService.logError('Recovery completed: $message');
    } catch (e) {
      debugPrint('ErrorRecoveryService: Failed to log recovery completion: $e');
    }
  }

  /// Check if recovery is in progress
  static bool get isRecovering => _isRecovering;

  /// Get recovery statistics
  static Map<String, dynamic> getRecoveryStats() {
    return {
      'isInitialized': _isInitialized,
      'isRecovering': _isRecovering,
      'recoveryAttempts': _recoveryAttempts,
      'maxRecoveryAttempts': _maxRecoveryAttempts,
    };
  }

  /// Reset recovery attempts
  static void resetRecoveryAttempts() {
    _recoveryAttempts = 0;
  }

  /// Force stop recovery
  static void stopRecovery() {
    _recoveryTimer?.cancel();
    _isRecovering = false;
  }

  /// Dispose resources
  static void dispose() {
    _recoveryTimer?.cancel();
    _isRecovering = false;
    _recoveryAttempts = 0;
    _isInitialized = false;
  }
}
