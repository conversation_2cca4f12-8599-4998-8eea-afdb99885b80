flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
media_kit: VideoOutput: video_output_dispose: 129595081985824
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
media_kit: VideoOutput: video_output_new: 129595070185392
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 129595070185392, id: 100769039710480, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 100769039710480
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
media_kit: VideoOutput: video_output_dispose: 129595071057248
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
media_kit: TextureGL: Resize: (1920, 540)
flutter: VideoOutput.Resize
flutter: {handle: 129595070185392, id: 100769039710480, rect: {left: 0, top: 0, width: 1920, height: 540}}
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: === MOVING TO NEXT VALID CAMPAIGN ===
flutter: Current campaign index: 0
flutter: Total campaigns: 2
flutter: Checking campaign 1: Broadband Compare Promo (trigger_type: 4)
flutter: Campaign Broadband Compare Promo does not meet trigger conditions, skipping
flutter: Checking campaign 0: General Campaign (trigger_type: 1)
flutter: Campaign General Campaign meets trigger conditions!
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: === MOVED TO CAMPAIGN General Campaign ===
flutter: Campaign has 4 schedule items
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: Proximity campaign distance 1.5 is within detected distance 1.5
flutter: CampaignController: Set _playingAudienceProximity = true
flutter: CampaignController: Transitioning to proximity campaign: Broadband Compare Promo
flutter: CampaignController: Interrupting current non-proximity campaign
flutter: Loaded 1 schedule items for campaign Broadband Compare Promo
flutter:   [0] Broadband_Compare_Promo.jpg
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Schedule item changed from BetterWithPepsi.mp4 to Broadband_Compare_Promo.jpg
flutter: CampaignController: Successfully transitioned to proximity campaign with 1 schedule items
flutter: CampaignController: Proximity content will play naturally until completion
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
media_kit: VideoOutput: video_output_dispose: 129595070185392
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: _onContentComplete called for Broadband_Compare_Promo.jpg
flutter: CampaignController: nextScheduleItem called - _playingAudienceProximity: true
flutter: CampaignController: Attempting to move from schedule item 0 to 1
flutter: CampaignController: Total schedule items in current campaign: 1
flutter: Finished all schedule items for campaign Broadband Compare Promo
flutter: Proximity campaign completed, calling proximityCampaignCompleted
flutter: CampaignController: Proximity campaign completed
flutter: CampaignController: All proximity campaigns completed, restoring previous non-proximity campaign
flutter: Loaded 4 schedule items for campaign General Campaign
flutter:   [0] BetterWithPepsi.mp4
flutter:   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
flutter:   [2] nzdeliveryapp_digi-eyeline-1920x540.png
flutter:   [3] 37327 Skinny Eyeline digital 1920x540.mp4
flutter: CampaignController: Restored to campaign index 0, advancing to next schedule index 1
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Ignoring campaign controller change during transition
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Ignoring campaign controller change during transition
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: _playingAudienceProximity: false
flutter: CampaignController: Proximity campaign distance 1.5 is within detected distance 0.81
flutter: CampaignController: Set _playingAudienceProximity = true
flutter: CampaignController: Transitioning to proximity campaign: Broadband Compare Promo
flutter: CampaignController: Interrupting current non-proximity campaign
flutter: Loaded 1 schedule items for campaign Broadband Compare Promo
flutter:   [0] Broadband_Compare_Promo.jpg
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Ignoring campaign controller change during transition
flutter: CampaignController: Successfully transitioned to proximity campaign with 1 schedule items
flutter: CampaignController: Proximity content will play naturally until completion
media_kit: VideoOutput: video_output_new: 129595071057248
Cannot load libcuda.so.1
media_kit: VideoOutput: Using H/W rendering.
flutter: VideoOutput.Resize
flutter: {handle: 129595071057248, id: 100769036208752, rect: {left: 0, top: 0, width: 1, height: 1}}
flutter: NativeVideoController: Texture ID: 100769036208752
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Schedule item changed from FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4 to Broadband_Compare_Promo.jpg
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
media_kit: VideoOutput: video_output_dispose: 129595071057248
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
flutter: PlayerScreen: Campaign controller changed
Lost connection to device.