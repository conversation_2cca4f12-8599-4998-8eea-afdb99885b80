import 'package:flutter/material.dart';
import 'package:signage/core/services/app_exit_service.dart';
import 'package:signage/core/services/data_fetch_service.dart';
import 'package:signage/core/services/cursor_manager.dart';
import 'package:signage/core/services/global_error_handler.dart';
import 'package:signage/core/services/error_recovery_service.dart';
import 'package:signage/core/services/app_health_monitor.dart';
import 'package:signage/core/services/crash_reporter_service.dart';
import 'package:signage/core/controllers/system_menu_controller.dart';
import 'package:signage/utils/platform_utils.dart';

/// A widget that displays a system menu for the player
/// This menu is shown when the user presses the back button on Android
/// or Ctrl+M on desktop platforms
class SystemMenuWidget extends StatefulWidget {
  /// Whether the menu is visible
  final bool isMenuVisible;

  /// Callback for when the menu visibility changes
  final Function(bool isVisible)? onMenuVisibilityChanged;

  /// Callback for when the player should be restarted
  final VoidCallback? onRestartPlayer;

  const SystemMenuWidget({
    super.key,
    this.isMenuVisible = false,
    this.onMenuVisibilityChanged,
    this.onRestartPlayer,
  });

  @override
  State<SystemMenuWidget> createState() => _SystemMenuWidgetState();
}

class _SystemMenuWidgetState extends State<SystemMenuWidget> {
  bool _isUpdating = false;
  double _updateProgress = 0.0;
  String _updateMessage = '';

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Full menu when visible
        if (widget.isMenuVisible)
          Positioned.fill(
            child: _buildFullMenu(),
          ),

        // Update progress overlay
        if (_isUpdating)
          Positioned.fill(
            child: _buildUpdateProgressOverlay(),
          ),
      ],
    );
  }

  /// Build the full menu overlay
  Widget _buildFullMenu() {
    return GestureDetector(
      onTap: _hideMenu,
      child: Container(
        color: Colors.black.withAlpha(179),
        child: Center(
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'System Menu',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildMenuOption(
                    icon: Icons.refresh,
                    label: 'Restart Player',
                    onTap: _restartPlayer,
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.system_update,
                    label: 'Check for Updates',
                    onTap: _checkForUpdates,
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.health_and_safety,
                    label: 'System Health',
                    onTap: () {
                      debugPrint('SystemMenuWidget: System Health menu item tapped');
                      _showSystemHealth();
                    },
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.healing,
                    label: 'Force Recovery',
                    onTap: () {
                      debugPrint('SystemMenuWidget: Force Recovery menu item tapped');
                      _forceRecovery();
                    },
                  ),
                  const Divider(),
                  _buildMenuOption(
                    icon: Icons.exit_to_app,
                    label: 'Exit',
                    onTap: _exitApp,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build a menu option
  Widget _buildMenuOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 16),
            Text(
              label,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the update progress overlay
  Widget _buildUpdateProgressOverlay() {
    return Container(
      color: Colors.black.withAlpha(204),
      child: Center(
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Checking for Updates',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _updateMessage,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: _updateProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 10,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Hide the menu
  void _hideMenu() {
    // Hide cursor when menu is dismissed on desktop platforms
    if (PlatformUtils.isDesktop) {
      CursorManager.instance.hideCursor();
    }

    if (widget.onMenuVisibilityChanged != null) {
      widget.onMenuVisibilityChanged!(false);
    }
  }

  /// Restart the player
  void _restartPlayer() {
    // First hide the menu
    _hideMenu();

    // Use a small delay to ensure the menu is hidden before restarting
    Future.delayed(const Duration(milliseconds: 100), () {
      if (widget.onRestartPlayer != null) {
        widget.onRestartPlayer!();
      }
    });
  }

  /// Check for updates
  Future<void> _checkForUpdates() async {
    _hideMenu();
    setState(() {
      _isUpdating = true;
      _updateProgress = 0.0;
      _updateMessage = 'Preparing to check for updates...';
    });

    final dataFetchService = DataFetchService(
      onProgress: (progress, message) {
        setState(() {
          _updateProgress = progress;
          _updateMessage = message;
        });
      },
      onComplete: () {
        setState(() {
          _isUpdating = false;
        });

        // Show a success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Update completed successfully. Restarting player...'),
            duration: Duration(seconds: 3),
          ),
        );

        // Restart the player after a short delay
        Future.delayed(const Duration(seconds: 3), () {
          if (widget.onRestartPlayer != null) {
            widget.onRestartPlayer!();
          }
        });
      },
      onError: (error) {
        setState(() {
          _isUpdating = false;
        });

        // Show an error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Update failed: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      },
    );

    await dataFetchService.fetchAllData();
  }

  /// Show system health information
  void _showSystemHealth() {
    debugPrint('SystemMenuWidget: _showSystemHealth called');
    _hideMenu();

    try {
        // Get health statistics with fallbacks
        final errorStats = GlobalErrorHandler.getErrorStats();
        final healthStatus = AppHealthMonitor.getHealthStatus();
        final crashStats = CrashReporterService.getCrashStats();
        final recoveryStats = ErrorRecoveryService.getRecoveryStats();

        // Provide fallback values if services aren't initialized
        final safeErrorStats = errorStats.isNotEmpty ? errorStats : {
          'errorCount': 0,
          'recentErrorsCount': 0,
          'isInitialized': false,
          'maxErrorsBeforeRestart': 5,
        };

        final safeHealthStatus = healthStatus.isNotEmpty ? healthStatus : {
          'isHealthy': true,
          'isMonitoring': false,
          'consecutiveFailures': 0,
          'errorsInLastMinute': 0,
          'lastMemoryUsage': 0.0,
          'lastCpuUsage': 0.0,
          'maxMemoryThreshold': 85.0,
          'maxCpuThreshold': 90.0,
          'maxConsecutiveFailures': 3,
        };

        final safeCrashStats = crashStats.isNotEmpty ? crashStats : {
          'isInitialized': false,
          'pendingReports': 0,
          'screenId': 'Not available',
          'appVersion': 'Unknown',
          'deviceInfo': 'Unknown',
        };

        final safeRecoveryStats = recoveryStats.isNotEmpty ? recoveryStats : {
          'isInitialized': false,
          'isRecovering': false,
          'recoveryAttempts': 0,
          'maxRecoveryAttempts': 3,
        };

        debugPrint('SystemMenuWidget: Showing health dialog with stats:');
        debugPrint('  Error Stats: $errorStats');
        debugPrint('  Health Status: $healthStatus');
        debugPrint('  Crash Stats: $crashStats');
        debugPrint('  Recovery Stats: $recoveryStats');

        debugPrint('SystemMenuWidget: About to show dialog');

        // Use the global navigator key to ensure dialog appears on top
        final navigatorState = SystemMenuController.navigatorKey.currentState;
        if (navigatorState != null) {
          showDialog(
            context: navigatorState.context,
            barrierDismissible: true,
            barrierColor: Colors.black87, // Make barrier more prominent
            builder: (BuildContext dialogContext) {
            debugPrint('SystemMenuWidget: Dialog builder called');
            return AlertDialog(
              backgroundColor: Colors.white,
              elevation: 24, // Higher elevation to ensure it's on top
              title: Row(
                children: [
                  Icon(Icons.health_and_safety, color: Colors.green),
                  SizedBox(width: 8),
                  Text('System Health'),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('Health Status: ${safeHealthStatus['isHealthy'] ? 'Healthy' : 'Unhealthy'}'),
                    Text('Error Count: ${safeErrorStats['errorCount']}'),
                    Text('Recent Errors: ${safeErrorStats['recentErrorsCount']}'),
                    Text('Memory Usage: ${(safeHealthStatus['lastMemoryUsage'] as double).toStringAsFixed(1)}%'),
                    Text('CPU Usage: ${(safeHealthStatus['lastCpuUsage'] as double).toStringAsFixed(1)}%'),
                    Text('Recovery Attempts: ${safeRecoveryStats['recoveryAttempts']}'),
                    Text('Pending Crash Reports: ${safeCrashStats['pendingReports']}'),
                    Text('Is Monitoring: ${safeHealthStatus['isMonitoring']}'),
                    Text('Is Recovering: ${safeRecoveryStats['isRecovering']}'),
                    const SizedBox(height: 16),
                    Text('Services Status:', style: TextStyle(fontWeight: FontWeight.bold)),
                    Text('Error Handler: ${safeErrorStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                    Text('Health Monitor: ${safeHealthStatus['isMonitoring'] ? 'Active' : 'Inactive'}'),
                    Text('Crash Reporter: ${safeCrashStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                    Text('Recovery Service: ${safeRecoveryStats['isInitialized'] ? 'Initialized' : 'Not Initialized'}'),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Close'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    GlobalErrorHandler.resetErrorCount();
                    AppHealthMonitor.resetHealthStatus();
                    ErrorRecoveryService.resetRecoveryAttempts();
                  },
                  child: const Text('Reset Stats'),
                ),
              ],
            );
          },
        );
        } else {
          debugPrint('SystemMenuWidget: Navigator state is null, cannot show dialog');
        }
      } catch (e) {
        debugPrint('Error showing system health dialog: $e');
        // Show a simple error dialog as fallback
        final navigatorState = SystemMenuController.navigatorKey.currentState;
        if (navigatorState != null) {
          showDialog(
            context: navigatorState.context,
            barrierColor: Colors.black87,
            builder: (BuildContext dialogContext) => AlertDialog(
            backgroundColor: Colors.white,
            elevation: 24,
            title: Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                SizedBox(width: 8),
                Text('System Health Error'),
              ],
            ),
            content: Text('Error loading health data: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
        } else {
          debugPrint('SystemMenuWidget: Navigator state is null, cannot show error dialog');
        }
      }
  }

  /// Force recovery
  void _forceRecovery() {
    debugPrint('SystemMenuWidget: _forceRecovery called');
    _hideMenu();

    // Use the global navigator key to ensure dialog appears on top
    final navigatorState = SystemMenuController.navigatorKey.currentState;
    if (navigatorState != null) {
      showDialog(
        context: navigatorState.context,
        barrierColor: Colors.black87, // Make barrier more prominent
        builder: (BuildContext dialogContext) => AlertDialog(
        backgroundColor: Colors.white,
        elevation: 24, // Higher elevation to ensure it's on top
        title: Row(
          children: [
            Icon(Icons.healing, color: Colors.orange),
            SizedBox(width: 8),
            Text('Force Recovery'),
          ],
        ),
        content: const Text('This will trigger application recovery. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              ErrorRecoveryService.triggerRecovery(
                'Manual recovery triggered from system menu',
                strategy: RecoveryStrategy.softRestart,
              );
            },
            child: const Text('Soft Restart'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              ErrorRecoveryService.triggerRecovery(
                'Manual hard recovery triggered from system menu',
                strategy: RecoveryStrategy.hardRestart,
              );
            },
            child: const Text('Hard Restart'),
          ),
        ],
      ),
    );
    } else {
      debugPrint('SystemMenuWidget: Navigator state is null, cannot show Force Recovery dialog');
    }
  }

  /// Exit the app
  void _exitApp() {
    // Hide the menu first
    _hideMenu();

    // Use a small delay to ensure the menu is hidden before exiting
    Future.delayed(const Duration(milliseconds: 100), () {
      // Use the AppExitService to properly exit the app
      AppExitService.exitApp();
    });
  }
}
